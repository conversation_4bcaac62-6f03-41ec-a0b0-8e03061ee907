﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <AssemblyVersion>1.0.11.0</AssemblyVersion>
    <FileVersion>1.0.11.0</FileVersion>
    <Version>1.0.11</Version>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>netstandard2.1</TargetFramework>
    <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <OutputPath>..\Release</OutputPath>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Thumb.png" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Thumb.png" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="MediaBrowser.Common" Version="4.8.10" />
    <PackageReference Include="MediaBrowser.Server.Core" Version="4.8.10" />
    <PackageReference Include="WebDav.Client" Version="2.9.0" PrivateAssets="none" />
  </ItemGroup>

<!--  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="Copy $(TargetDir)$(TargetFileName) $(SolutionDir)$(ConfigurationName)\$(TargetFileName) /y&#xD;&#xA;" />
  </Target>-->

</Project>
